-- CreateTable
CREATE TABLE "pks" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "numero" TEXT NOT NULL,
    "ubicacion_gps" TEXT NOT NULL,
    "estado" TEXT NOT NULL,
    "asign<PERSON><PERSON>" INTEGER,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "pks_asignadoA_fkey" FOREIGN KEY ("asignadoA") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "pks_numero_key" ON "pks"("numero");

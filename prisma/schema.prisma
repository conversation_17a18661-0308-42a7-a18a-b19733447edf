// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Enum para roles de usuario
enum UserRole {
  ADMIN
  ENGINEER
  SOCIAL_ASSISTANT
}

// Modelo de Usuario
model User {
  id              Int      @id @default(autoincrement())
  email           String   @unique
  hashed_password String
  role            UserRole
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@map("users")
}

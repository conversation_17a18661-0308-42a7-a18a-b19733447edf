generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id              Int      @id @default(autoincrement())
  email           String   @unique
  hashed_password String
  role            String
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  pks             PK[]

  @@map("users")
}

model PK {
  id            Int      @id @default(autoincrement())
  numero        String   @unique
  ubicacion_gps String
  estado        String
  asignadoA     Int?
  created_at    DateTime @default(now())
  usuario       User?    @relation(fields: [asignadoA], references: [id])

  @@map("pks")
}

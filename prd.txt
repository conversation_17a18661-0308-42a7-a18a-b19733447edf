Backlog Técnico - Aplicación de Actas de Vecindad

🧩 Épica 1: Autenticación y Control de Acceso

Historia de Usuario 1.1

Como profesional (ingeniero o auxiliar),
quiero iniciar sesión con credenciales seguras,
para acceder a las funcionalidades según mi rol.

Tareas Técnicas
	•	Crear esquema users con campos: id, email, hashed_password, role, created_at, updated_at.
	•	Configurar Auth.js (NextAuth) con proveedor de credenciales personalizadas.
	•	Implementar middleware de control de sesión.
	•	Crear UI de login con validación, loading, error.
	•	Crear pruebas unitarias para lógica de login (validaciones, errores).
	•	Crear pruebas de integración de flujo de autenticación.

⸻

🧩 Épica 2: Gestión de PKs y Asignación de Visitas

Historia de Usuario 2.1

Como profesional en campo,
quiero ver los PKs asignados,
para iniciar la visita correspondiente.

Tareas Técnicas
	•	Crear tabla pks con campos: id, numero, ubicacion_gps, estado, asignadoA, created_at.
	•	Crear endpoint GET /api/pks filtrado por usuario logueado.
	•	Crear componente PKList para mostrar PKs asignados.
	•	Crear pruebas unitarias para el endpoint y componente.

Historia de Usuario 2.2

Como administrador,
quiero asignar PKs a usuarios,
para distribuir eficientemente las tareas semanales.

Tareas Técnicas
	•	Crear UI de asignación (autocomplete de usuarios, multiselect de PKs).
	•	Endpoint POST /api/pks/asignar.
	•	Validar permisos de administrador en backend.
	•	Pruebas unitarias de lógica de asignación y validación.
	•	Pruebas E2E de flujo de asignación desde UI.

⸻

🧩 Épica 3: Levantamiento Técnico y Social

Historia de Usuario 3.1

Como ingeniero,
quiero registrar mediciones y fotos del predio,
para documentar su estado estructural.

Tareas Técnicas
	•	Crear tabla visitas con campos: id, pk_id, user_id, fecha, datos_tecnicos, imagenes, pdf_acta, estado.
	•	Crear formulario de medición técnica: distancia, frente, estado fachada, observaciones.
	•	Componente de carga de fotos (Cloudinary o S3).
	•	Validaciones con Zod.
	•	Pruebas unitarias de validaciones.
	•	Pruebas E2E de flujo de visita técnica.

Historia de Usuario 3.2

Como auxiliar social,
quiero completar el formulario socio-demográfico y registrar la firma,
para tener un acta completa y válida.

Tareas Técnicas
	•	Crear esquema de formulario social: tipo de tenencia, ocupantes, servicios, observaciones.
	•	Componente de captura de firma (lienzo + exportación a imagen).
	•	Guardado de firma como imagen en storage.
	•	Asociación de firma al acta.
	•	Validaciones y pruebas de integridad de datos.
	•	Pruebas visuales de captura de firma.

⸻

🧩 Épica 4: Generación y Almacenamiento de Actas

Historia de Usuario 4.1

Como profesional en campo,
quiero generar un acta en PDF al finalizar la visita,
para compartirlo y archivarlo formalmente.

Tareas Técnicas
	•	Crear plantilla de PDF (plantilla HTML con estilos).
	•	Usar PDF-lib o Puppeteer para generar el PDF.
	•	Subir PDF a Cloudinary/S3 y obtener URL.
	•	Guardar enlace del acta en la tabla visitas.
	•	Crear botón “Generar Acta” en UI.
	•	Pruebas de generación de PDF con datos dummy.
	•	Validaciones en backend para control de duplicados.

⸻

🧩 Épica 5: Panel de Gestión y Visualización

Historia de Usuario 5.1

Como profesional,
quiero ver un resumen de mis visitas completadas,
para hacer seguimiento del avance de trabajo.

Historia de Usuario 5.2

Como administrador,
quiero visualizar todas las visitas, sus estados y documentos generados,
para monitorear el progreso del equipo.

Tareas Técnicas
	•	Crear dashboard con tabs para “Visitas en curso” y “Completadas”.
	•	Componentes de tabla con columnas: PK, fecha, estado, enlace a PDF.
	•	Filtros por fecha, estado, usuario.
	•	Pruebas de integración de filtros y enlaces.
	•	Pruebas de permisos de visualización por rol.

⸻

⏱️ Prioridad para MVP
	1.	Épica 1: Autenticación
	2.	Épica 2: Gestión de PKs (sin asignación dinámica)
	3.	Épica 3: Formulario técnico + social + fotos
	4.	Épica 4: Generación básica de PDF
	5.	Épica 5: Visualización simple de visitas

⸻
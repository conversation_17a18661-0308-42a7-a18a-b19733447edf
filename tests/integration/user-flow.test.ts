import { describe, it, expect } from '@jest/globals'
import { UserFactory } from '../../src/factories/UserFactory'
import { NextRequest } from 'next/server'

// Helper para crear mock de NextRequest
function createMockRequest(body: any): NextRequest {
  return {
    json: jest.fn().mockResolvedValue(body),
    method: 'POST',
    url: 'http://localhost:3000/api/users'
  } as any
}

describe('User Flow Integration Tests', () => {
  // Database cleanup is handled globally in tests/setup.ts

  describe('Complete User Creation Flow', () => {
    it('should create user through complete flow: Controller -> UseCase -> Service -> Repository -> Prisma', async () => {
      // Arrange
      const userController = UserFactory.createUserController()
      const requestBody = {
        email: '<EMAIL>',
        password: 'password123',
        role: 'ENGINEER'
      }
      const mockRequest = createMockRequest(requestBody)

      // Act
      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      // Assert
      expect(response.status).toBe(201)
      expect(responseData).toHaveProperty('user')
      expect(responseData.user).toMatchObject({
        email: '<EMAIL>',
        role: 'ENGINEER'
      })
      expect(responseData.user).toHaveProperty('id')
      expect(responseData.user).toHaveProperty('created_at')
      expect(responseData.user).toHaveProperty('updated_at')
      expect(responseData.user).not.toHaveProperty('hashed_password')
    })

    it('should handle duplicate email error through complete flow', async () => {
      // Arrange
      const userController = UserFactory.createUserController()
      const requestBody = {
        email: '<EMAIL>',
        password: 'password123',
        role: 'ENGINEER'
      }

      // Act - Create first user
      const mockRequest1 = createMockRequest(requestBody)
      const response1 = await userController.create(mockRequest1)

      // Act - Try to create duplicate user
      const mockRequest2 = createMockRequest(requestBody)
      const response2 = await userController.create(mockRequest2)
      const responseData2 = await response2.json()

      // Assert
      expect(response1.status).toBe(201)
      expect(response2.status).toBe(400)
      expect(responseData2.error).toBe('Email already exists')
    })

    it('should validate email format through complete flow', async () => {
      // Arrange
      const userController = UserFactory.createUserController()
      const requestBody = {
        email: 'invalid-email',
        password: 'password123',
        role: 'ENGINEER'
      }
      const mockRequest = createMockRequest(requestBody)

      // Act
      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      // Assert
      expect(response.status).toBe(400)
      expect(responseData.error).toBe('Invalid email format')
    })

    it('should validate password strength through complete flow', async () => {
      // Arrange
      const userController = UserFactory.createUserController()
      const requestBody = {
        email: '<EMAIL>',
        password: '123',
        role: 'ENGINEER'
      }
      const mockRequest = createMockRequest(requestBody)

      // Act
      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      // Assert
      expect(response.status).toBe(400)
      expect(responseData.error).toBe('Password must be at least 6 characters long')
    })

    it('should validate role through complete flow', async () => {
      // Arrange
      const userController = UserFactory.createUserController()
      const requestBody = {
        email: '<EMAIL>',
        password: 'password123',
        role: 'INVALID_ROLE'
      }
      const mockRequest = createMockRequest(requestBody)

      // Act
      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      // Assert
      expect(response.status).toBe(400)
      expect(responseData.error).toBe('Invalid role')
    })

    it('should normalize email (trim and lowercase) through complete flow', async () => {
      // Arrange
      const userController = UserFactory.createUserController()
      const requestBody = {
        email: '  <EMAIL>  ',
        password: 'password123',
        role: 'ENGINEER'
      }
      const mockRequest = createMockRequest(requestBody)

      // Act
      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      // Assert
      expect(response.status).toBe(201)
      expect(responseData.user.email).toBe('<EMAIL>')
    })

    it('should store hashed password in database', async () => {
      // Arrange
      const userController = UserFactory.createUserController()
      const userRepository = UserFactory.createUserRepository()
      const requestBody = {
        email: '<EMAIL>',
        password: 'password123',
        role: 'ENGINEER'
      }
      const mockRequest = createMockRequest(requestBody)

      // Act
      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      // Verify password is hashed in database
      const userInDb = await userRepository.findById(responseData.user.id)

      // Assert
      expect(response.status).toBe(201)
      expect(userInDb?.hashed_password).not.toBe('password123')
      expect(userInDb?.hashed_password).toMatch(/^\$2[aby]\$/)
    })
  })

  describe('Service Layer Integration', () => {
    it('should validate password correctly', async () => {
      // Arrange
      const userService = UserFactory.createUserService()
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        role: 'ENGINEER' as const
      }

      // Act
      const user = await userService.createUser(userData)
      const isValidPassword = await userService.validatePassword('password123', user)
      const isInvalidPassword = await userService.validatePassword('wrongpassword', user)

      // Assert
      expect(isValidPassword).toBe(true)
      expect(isInvalidPassword).toBe(false)
    })

    it('should get user by email', async () => {
      // Arrange
      const userService = UserFactory.createUserService()
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        role: 'ADMIN' as const
      }

      // Act
      const createdUser = await userService.createUser(userData)
      const foundUser = await userService.getUserByEmail('<EMAIL>')

      // Assert
      expect(foundUser).toBeDefined()
      expect(foundUser?.id).toBe(createdUser.id)
      expect(foundUser?.email).toBe('<EMAIL>')
    })
  })
})

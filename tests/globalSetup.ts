import { execSync } from 'child_process'
import { join } from 'path'

export default async function globalSetup() {
  console.log('🔧 Configurando entorno de testing...')
  
  // Configurar base de datos de testing
  process.env.DATABASE_URL = 'file:./test.db'
  
  try {
    // Aplicar migraciones a la base de datos de testing
    console.log('📦 Aplicando migraciones de Prisma...')
    execSync('npx prisma db push --force-reset', {
      cwd: join(__dirname, '..'),
      stdio: 'inherit',
      env: {
        ...process.env,
        DATABASE_URL: 'file:./test.db'
      }
    })
    
    console.log('✅ Entorno de testing configurado correctamente')
  } catch (error) {
    console.error('❌ Error configurando entorno de testing:', error)
    throw error
  }
}

import { beforeEach, afterEach } from '@jest/globals'
import { prisma } from '../lib/prisma'

// Configuración global para todos los tests
beforeEach(async () => {
  // Limpiar la base de datos antes de cada test
  // Esto asegura que cada test comience con un estado limpio
  await cleanDatabase()
})

afterEach(async () => {
  // Limpiar después de cada test también
  await cleanDatabase()
})

// Función para limpiar la base de datos
async function cleanDatabase() {
  // Obtener todas las tablas de la base de datos
  const tablenames = await prisma.$queryRaw<Array<{ name: string }>>`
    SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '_prisma_migrations';
  `
  
  // Limpiar cada tabla
  for (const { name } of tablenames) {
    await prisma.$executeRawUnsafe(`DELETE FROM "${name}";`)
  }
}

// Configurar timeout global para tests
jest.setTimeout(10000)

import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { CreateUserUseCase } from '../../../src/use-cases/CreateUserUseCase'
import { User, CreateUserRequest } from '../../../src/types/entities/User'

// Mock del UserService
const mockUserService = {
  createUser: jest.fn(),
  getUserById: jest.fn(),
  getUserByEmail: jest.fn(),
  getAllUsers: jest.fn(),
  validatePassword: jest.fn(),
  updateUser: jest.fn(),
  deleteUser: jest.fn()
} as any

describe('CreateUserUseCase', () => {
  let createUserUseCase: CreateUserUseCase

  beforeEach(() => {
    jest.clearAllMocks()
    createUserUseCase = new CreateUserUseCase(mockUserService)
  })

  describe('execute', () => {
    const validRequest: CreateUserRequest = {
      email: '<EMAIL>',
      password: 'password123',
      role: 'ENGINEER'
    }

    it('should create user successfully', async () => {
      const expectedUser: User = {
        id: 1,
        email: validRequest.email,
        hashed_password: 'hashedpassword',
        role: validRequest.role,
        created_at: new Date(),
        updated_at: new Date()
      }

      mockUserService.createUser.mockResolvedValue(expectedUser)

      const result = await createUserUseCase.execute(validRequest)

      expect(mockUserService.createUser).toHaveBeenCalledWith({
        email: validRequest.email,
        password: validRequest.password,
        role: validRequest.role
      })
      expect(result).toEqual({
        user: {
          id: expectedUser.id,
          email: expectedUser.email,
          role: expectedUser.role,
          created_at: expectedUser.created_at,
          updated_at: expectedUser.updated_at
        }
      })
    })

    it('should exclude hashed_password from response', async () => {
      const expectedUser: User = {
        id: 1,
        email: validRequest.email,
        hashed_password: 'hashedpassword',
        role: validRequest.role,
        created_at: new Date(),
        updated_at: new Date()
      }

      mockUserService.createUser.mockResolvedValue(expectedUser)

      const result = await createUserUseCase.execute(validRequest)

      expect(result.user).not.toHaveProperty('hashed_password')
    })

    it('should handle service errors', async () => {
      const serviceError = new Error('Email already exists')
      mockUserService.createUser.mockRejectedValue(serviceError)

      await expect(createUserUseCase.execute(validRequest)).rejects.toThrow('Email already exists')
    })

    it('should validate required fields', async () => {
      const invalidRequest = {
        email: '',
        password: '',
        role: '' as any
      }

      await expect(createUserUseCase.execute(invalidRequest)).rejects.toThrow('Email is required')
    })

    it('should validate email field', async () => {
      const invalidRequest = {
        ...validRequest,
        email: ''
      }

      await expect(createUserUseCase.execute(invalidRequest)).rejects.toThrow('Email is required')
    })

    it('should validate password field', async () => {
      const invalidRequest = {
        ...validRequest,
        password: ''
      }

      await expect(createUserUseCase.execute(invalidRequest)).rejects.toThrow('Password is required')
    })

    it('should validate role field', async () => {
      const invalidRequest = {
        ...validRequest,
        role: '' as any
      }

      await expect(createUserUseCase.execute(invalidRequest)).rejects.toThrow('Role is required')
    })

    it('should trim email whitespace', async () => {
      const requestWithWhitespace = {
        ...validRequest,
        email: '  <EMAIL>  '
      }

      const expectedUser: User = {
        id: 1,
        email: '<EMAIL>',
        hashed_password: 'hashedpassword',
        role: validRequest.role,
        created_at: new Date(),
        updated_at: new Date()
      }

      mockUserService.createUser.mockResolvedValue(expectedUser)

      await createUserUseCase.execute(requestWithWhitespace)

      expect(mockUserService.createUser).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: requestWithWhitespace.password,
        role: requestWithWhitespace.role
      })
    })

    it('should convert email to lowercase', async () => {
      const requestWithUppercase = {
        ...validRequest,
        email: '<EMAIL>'
      }

      const expectedUser: User = {
        id: 1,
        email: '<EMAIL>',
        hashed_password: 'hashedpassword',
        role: validRequest.role,
        created_at: new Date(),
        updated_at: new Date()
      }

      mockUserService.createUser.mockResolvedValue(expectedUser)

      await createUserUseCase.execute(requestWithUppercase)

      expect(mockUserService.createUser).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: requestWithUppercase.password,
        role: requestWithUppercase.role
      })
    })
  })
})

import { unlink } from 'fs/promises'
import { join } from 'path'

export default async function globalTeardown() {
  console.log('🧹 Limpiando entorno de testing...')
  
  try {
    // Eliminar base de datos de testing
    const testDbPath = join(__dirname, '..', 'test.db')
    await unlink(testDbPath)
    console.log('✅ Base de datos de testing eliminada')
  } catch (error) {
    // No es crítico si no se puede eliminar el archivo
    console.log('ℹ️ No se pudo eliminar la base de datos de testing (puede que no exista)')
  }
  
  console.log('✅ Limpieza completada')
}

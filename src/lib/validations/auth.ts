import { z } from "zod"

/**
 * Schema de validación para las credenciales de login
 */
export const signInSchema = z.object({
  email: z
    .string({ required_error: "El email es requerido" })
    .min(1, "El email es requerido")
    .email("Formato de email inválido")
    .max(255, "El email no puede exceder 255 caracteres"),
  password: z
    .string({ required_error: "La contraseña es requerida" })
    .min(1, "La contraseña es requerida")
    .min(8, "La contraseña debe tener al menos 8 caracteres")
    .max(100, "La contraseña no puede exceder 100 caracteres"),
})

/**
 * Tipo TypeScript inferido del schema de validación
 */
export type SignInInput = z.infer<typeof signInSchema>

/**
 * Función para validar las credenciales de forma segura
 * @param credentials - Credenciales a validar
 * @returns Resultado de la validación con datos parseados o errores
 */
export function validateSignInCredentials(credentials: unknown) {
  return signInSchema.safeParse(credentials)
}

/**
 * @fileoverview User service layer implementing business logic for user operations.
 * This module contains the business logic for user management, including validation,
 * password hashing, and coordination between the repository and use case layers.
 */

import bcrypt from 'bcryptjs'
import { UserRepositoryInterface, UpdateUserRepositoryData } from '../repositories/user'
import { User, UserFilters, CreateUserData } from '../types/entities/User'

/**
 * Service class handling user business logic and operations.
 *
 * @description Implements business rules and validation for user operations.
 * Acts as an intermediary between use cases and the repository layer,
 * handling concerns like password hashing, email validation, and business rules.
 *
 * @example
 * ```typescript
 * const userService = new UserService(userRepository)
 * const user = await userService.createUser({
 *   email: '<EMAIL>',
 *   password: 'securePassword123',
 *   role: 'ENGINEER'
 * })
 * ```
 */
export class UserService {
  /**
   * Creates a new UserService instance.
   *
   * @param userRepository - Repository implementation for user data access
   */
  constructor(private userRepository: UserRepositoryInterface) {}

  /**
   * Creates a new user with business logic validation.
   *
   * @param data - User creation data with plain text password
   * @returns Promise resolving to the created user
   * @throws Error if validation fails or email already exists
   *
   * @example
   * ```typescript
   * const userData = {
   *   email: '<EMAIL>',
   *   password: 'strongPassword123',
   *   role: 'ENGINEER'
   * }
   * const user = await userService.createUser(userData)
   * ```
   */
  async createUser(data: CreateUserData): Promise<User> {
    // Validaciones de negocio
    this.validateEmail(data.email)
    this.validatePasswordStrength(data.password)
    this.validateRole(data.role)

    // Verificar que el email no exista
    await this.ensureEmailIsUnique(data.email)

    // Hash de la contraseña
    const hashedPassword = await bcrypt.hash(data.password, 10)

    // Crear usuario
    return this.userRepository.create({
      email: data.email,
      hashed_password: hashedPassword,
      role: data.role
    })
  }

  /**
   * Retrieves a user by their unique identifier.
   *
   * @param id - User's unique identifier
   * @returns Promise resolving to the user
   * @throws Error if user not found
   *
   * @example
   * ```typescript
   * const user = await userService.getUserById(123)
   * console.log(`User: ${user.email}`)
   * ```
   */
  async getUserById(id: number): Promise<User> {
    const user = await this.userRepository.findById(id)
    if (!user) {
      throw new Error('User not found')
    }
    return user
  }

  /**
   * Retrieves a user by their email address.
   *
   * @param email - User's email address
   * @returns Promise resolving to user or null if not found
   *
   * @example
   * ```typescript
   * const user = await userService.getUserByEmail('<EMAIL>')
   * if (user) {
   *   console.log('User found')
   * }
   * ```
   */
  async getUserByEmail(email: string): Promise<User | null> {
    return this.userRepository.findByEmail(email)
  }

  /**
   * Retrieves all users, optionally filtered by criteria.
   *
   * @param filters - Optional filtering criteria
   * @returns Promise resolving to array of users
   *
   * @example
   * ```typescript
   * const allUsers = await userService.getAllUsers()
   * const engineers = await userService.getAllUsers({ role: 'ENGINEER' })
   * ```
   */
  async getAllUsers(filters?: UserFilters): Promise<User[]> {
    return this.userRepository.findAll(filters)
  }

  /**
   * Validates a plain text password against a user's hashed password.
   *
   * @param plainPassword - Plain text password to validate
   * @param user - User object containing the hashed password
   * @returns Promise resolving to true if password is valid, false otherwise
   *
   * @example
   * ```typescript
   * const isValid = await userService.validatePassword('userPassword', user)
   * if (isValid) {
   *   console.log('Password is correct')
   * }
   * ```
   */
  async validatePassword(plainPassword: string, user: User): Promise<boolean> {
    return bcrypt.compare(plainPassword, user.hashed_password)
  }

  /**
   * Updates an existing user's information with validation.
   *
   * @param id - User's unique identifier
   * @param data - Partial user data to update
   * @returns Promise resolving to the updated user
   * @throws Error if user not found, validation fails, or email already exists
   *
   * @example
   * ```typescript
   * const updatedUser = await userService.updateUser(123, {
   *   email: '<EMAIL>',
   *   role: 'ADMIN'
   * })
   * ```
   */
  async updateUser(id: number, data: Partial<CreateUserData>): Promise<User> {
    // Verificar que el usuario existe
    await this.getUserById(id)

    const updateData: Partial<UpdateUserRepositoryData> = {}

    if (data.email) {
      this.validateEmail(data.email)
      // Verificar que el nuevo email no esté en uso por otro usuario
      const existingUser = await this.userRepository.findByEmail(data.email)
      if (existingUser && existingUser.id !== id) {
        throw new Error('Email already exists')
      }
      updateData.email = data.email
    }

    if (data.password) {
      this.validatePasswordStrength(data.password)
      updateData.hashed_password = await bcrypt.hash(data.password, 10)
    }

    if (data.role) {
      this.validateRole(data.role)
      updateData.role = data.role
    }

    return this.userRepository.update(id, updateData)
  }

  /**
   * Permanently deletes a user from the system.
   *
   * @param id - User's unique identifier
   * @returns Promise that resolves when deletion is complete
   * @throws Error if user not found
   *
   * @example
   * ```typescript
   * await userService.deleteUser(123)
   * console.log('User deleted successfully')
   * ```
   */
  async deleteUser(id: number): Promise<void> {
    // Verificar que el usuario existe
    await this.getUserById(id)

    return this.userRepository.delete(id)
  }

  // Private validation methods

  /**
   * Validates email format using regex pattern.
   *
   * @param email - Email address to validate
   * @throws Error if email format is invalid
   * @private
   */
  private validateEmail(email: string): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format')
    }
  }

  /**
   * Validates password strength requirements.
   *
   * @param password - Password to validate
   * @throws Error if password doesn't meet strength requirements
   * @private
   */
  private validatePasswordStrength(password: string): void {
    if (password.length < 6) {
      throw new Error('Password must be at least 6 characters long')
    }
  }

  /**
   * Validates that the role is one of the allowed values.
   *
   * @param role - User role to validate
   * @throws Error if role is not valid
   * @private
   */
  private validateRole(role: string): void {
    const validRoles = ['ADMIN', 'ENGINEER', 'SOCIAL_ASSISTANT']
    if (!validRoles.includes(role)) {
      throw new Error('Invalid role')
    }
  }

  /**
   * Ensures that an email address is not already in use.
   *
   * @param email - Email address to check
   * @throws Error if email already exists
   * @private
   */
  private async ensureEmailIsUnique(email: string): Promise<void> {
    const exists = await this.userRepository.existsByEmail(email)
    if (exists) {
      throw new Error('Email already exists')
    }
  }
}

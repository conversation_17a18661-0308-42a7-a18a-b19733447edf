/**
 * @fileoverview Use case for creating new users in the system.
 * This module implements the Create User use case following Clean Architecture principles,
 * orchestrating the user creation flow while maintaining separation of concerns.
 */

import { UserService } from '../services/UserService'
import { CreateUserRequest, CreateUserResponse, UserR<PERSON>ponse, User } from '../types/entities/User'

/**
 * Use case for creating new users with validation and business logic.
 *
 * @description Implements the Create User use case following Clean Architecture.
 * Handles request validation, data normalization, and coordinates with the
 * service layer to create users while ensuring proper response formatting.
 *
 * @example
 * ```typescript
 * const createUserUseCase = new CreateUserUseCase(userService)
 * const response = await createUserUseCase.execute({
 *   email: '<EMAIL>',
 *   password: 'securePassword123',
 *   role: 'ENGINEER'
 * })
 * console.log(`Created user: ${response.user.email}`)
 * ```
 */
export class CreateUserUseCase {
  /**
   * Creates a new CreateUserUseCase instance.
   *
   * @param userService - Service layer instance for user operations
   */
  constructor(private userService: UserService) {}

  /**
   * Executes the create user use case.
   *
   * @param request - User creation request with email, password, and role
   * @returns Promise resolving to the creation response with user data (excluding sensitive info)
   * @throws Error if validation fails or user creation fails
   *
   * @example
   * ```typescript
   * const request = {
   *   email: '<EMAIL>',
   *   password: 'strongPassword123',
   *   role: 'ENGINEER'
   * }
   * const response = await useCase.execute(request)
   * ```
   */
  async execute(request: CreateUserRequest): Promise<CreateUserResponse> {
    // Validar campos requeridos
    this.validateRequest(request)

    // Normalizar datos
    const normalizedRequest = this.normalizeRequest(request)

    // Crear usuario usando el servicio
    const user = await this.userService.createUser(normalizedRequest)

    // Retornar respuesta sin datos sensibles
    return {
      user: this.toUserResponse(user)
    }
  }

  /**
   * Validates the incoming request for required fields.
   *
   * @param request - User creation request to validate
   * @throws Error if any required field is missing or empty
   * @private
   */
  private validateRequest(request: CreateUserRequest): void {
    if (!request.email || request.email.trim() === '') {
      throw new Error('Email is required')
    }

    if (!request.password || request.password.trim() === '') {
      throw new Error('Password is required')
    }

    if (!request.role || request.role.trim() === '') {
      throw new Error('Role is required')
    }
  }

  /**
   * Normalizes the request data for consistent processing.
   *
   * @param request - User creation request to normalize
   * @returns Normalized request with trimmed and lowercased email
   * @private
   */
  private normalizeRequest(request: CreateUserRequest): CreateUserRequest {
    return {
      email: request.email.trim().toLowerCase(),
      password: request.password,
      role: request.role
    }
  }

  /**
   * Converts a user entity to a safe response format.
   *
   * @param user - User entity from the service layer
   * @returns User response object without sensitive information
   * @private
   */
  private toUserResponse(user: User): UserResponse {
    return {
      id: user.id,
      email: user.email,
      role: user.role,
      created_at: user.created_at,
      updated_at: user.updated_at
    }
  }
}

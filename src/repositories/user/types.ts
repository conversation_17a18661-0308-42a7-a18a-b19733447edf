/**
 * @fileoverview Repository interfaces and types for user data access layer.
 * This module defines the contract for user repository implementations,
 * ensuring consistent data access patterns across different storage backends.
 */

import { User, UserFilters, UserRole } from '../../types/entities/User'

/**
 * Interface defining the contract for user repository implementations.
 *
 * @description Provides a consistent API for user data access operations
 * regardless of the underlying storage mechanism (database, mock, etc.).
 * Follows the Repository pattern to abstract data access logic.
 */
export interface UserRepositoryInterface {
  /**
   * Creates a new user in the repository.
   *
   * @param data - User data with hashed password
   * @returns Promise resolving to the created user
   * @throws Error if email already exists or validation fails
   */
  create(data: CreateUserRepositoryData): Promise<User>

  /**
   * Finds a user by their unique identifier.
   *
   * @param id - User's unique identifier
   * @returns Promise resolving to user or null if not found
   */
  findById(id: number): Promise<User | null>

  /**
   * Finds a user by their email address.
   *
   * @param email - User's email address
   * @returns Promise resolving to user or null if not found
   */
  findByEmail(email: string): Promise<User | null>

  /**
   * Retrieves all users, optionally filtered by criteria.
   *
   * @param filters - Optional filtering criteria
   * @returns Promise resolving to array of users
   */
  findAll(filters?: UserFilters): Promise<User[]>

  /**
   * Updates an existing user's information.
   *
   * @param id - User's unique identifier
   * @param data - Partial user data to update
   * @returns Promise resolving to the updated user
   * @throws Error if user not found
   */
  update(id: number, data: UpdateUserRepositoryData): Promise<User>

  /**
   * Permanently deletes a user from the repository.
   *
   * @param id - User's unique identifier
   * @returns Promise that resolves when deletion is complete
   * @throws Error if user not found
   */
  delete(id: number): Promise<void>

  /**
   * Checks if a user with the given email exists.
   *
   * @param email - Email address to check
   * @returns Promise resolving to true if user exists, false otherwise
   */
  existsByEmail(email: string): Promise<boolean>

  /**
   * Finds all users with a specific role.
   *
   * @param role - User role to filter by
   * @returns Promise resolving to array of users with the specified role
   */
  findByRole(role: UserRole): Promise<User[]>

  /**
   * Counts the total number of users, optionally filtered.
   *
   * @param filters - Optional filtering criteria
   * @returns Promise resolving to the count of users
   */
  count(filters?: UserFilters): Promise<number>
}

/**
 * Data structure for creating a user at the repository level.
 *
 * @description Contains pre-processed data ready for storage,
 * including the hashed password.
 */
export interface CreateUserRepositoryData {
  /** User's email address (must be unique) */
  email: string
  /** Pre-hashed password ready for storage */
  hashed_password: string
  /** User's role in the system */
  role: UserRole
}

/**
 * Data structure for updating user information at the repository level.
 *
 * @description All fields are optional to support partial updates.
 * Contains pre-processed data ready for storage.
 */
export interface UpdateUserRepositoryData {
  /** Updated email address */
  email?: string
  /** Updated hashed password */
  hashed_password?: string
  /** Updated user role */
  role?: UserRole
}



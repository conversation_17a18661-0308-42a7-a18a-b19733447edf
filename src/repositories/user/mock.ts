/**
 * @fileoverview Mock implementation of UserRepository for testing purposes.
 * This module provides an in-memory implementation of the user repository
 * that can be used in unit tests and development environments.
 */

import { UserRepositoryInterface, CreateUserRepositoryData, UpdateUserRepositoryData } from './types'
import { User, UserFilters, UserRole } from '../../types/entities/User'

/**
 * In-memory mock implementation of user repository for testing.
 *
 * @description Provides a complete implementation of UserRepositoryInterface
 * using in-memory storage. Useful for unit testing and development scenarios
 * where a real database connection is not needed or desired.
 *
 * @example
 * ```typescript
 * const mockRepo = new MockUserRepository()
 * const user = await mockRepo.create({
 *   email: '<EMAIL>',
 *   hashed_password: 'hashedPassword',
 *   role: 'ENGINEER'
 * })
 * ```
 */
export class MockUserRepository implements UserRepositoryInterface {
  /** In-memory storage for user data */
  private users: User[] = []
  /** Auto-incrementing ID counter */
  private nextId = 1

  /**
   * Creates a new user in memory.
   *
   * @param data - User data with hashed password
   * @returns Promise resolving to the created user
   * @throws Error if email already exists
   */
  async create(data: CreateUserRepositoryData): Promise<User> {
    // Check for duplicate email
    const existingUser = await this.findByEmail(data.email)
    if (existingUser) {
      throw new Error('Email already exists')
    }

    const user: User = {
      id: this.nextId++,
      email: data.email,
      hashed_password: data.hashed_password,
      role: data.role,
      created_at: new Date(),
      updated_at: new Date()
    }
    this.users.push(user)
    return user
  }

  /**
   * Finds a user by their unique identifier.
   *
   * @param id - User's unique identifier
   * @returns Promise resolving to user or null if not found
   */
  async findById(id: number): Promise<User | null> {
    return this.users.find(user => user.id === id) || null
  }

  /**
   * Finds a user by their email address.
   *
   * @param email - User's email address
   * @returns Promise resolving to user or null if not found
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.users.find(user => user.email === email) || null
  }

  /**
   * Retrieves all users, optionally filtered by criteria.
   *
   * @param filters - Optional filtering criteria
   * @returns Promise resolving to array of users matching the filters
   */
  async findAll(filters?: UserFilters): Promise<User[]> {
    let result = [...this.users]

    if (filters?.email) {
      result = result.filter(user => user.email === filters.email)
    }

    if (filters?.role) {
      result = result.filter(user => user.role === filters.role)
    }

    return result
  }

  /**
   * Updates an existing user's information.
   *
   * @param id - User's unique identifier
   * @param data - Partial user data to update
   * @returns Promise resolving to the updated user
   * @throws Error if user not found
   */
  async update(id: number, data: UpdateUserRepositoryData): Promise<User> {
    const userIndex = this.users.findIndex(user => user.id === id)
    if (userIndex === -1) {
      throw new Error('User not found')
    }

    const updatedUser = {
      ...this.users[userIndex],
      ...data,
      updated_at: new Date()
    }

    this.users[userIndex] = updatedUser
    return updatedUser
  }

  /**
   * Permanently deletes a user from memory.
   *
   * @param id - User's unique identifier
   * @returns Promise that resolves when deletion is complete
   * @throws Error if user not found
   */
  async delete(id: number): Promise<void> {
    const userIndex = this.users.findIndex(user => user.id === id)
    if (userIndex === -1) {
      throw new Error('User not found')
    }
    this.users.splice(userIndex, 1)
  }

  /**
   * Checks if a user with the given email exists.
   *
   * @param email - Email address to check
   * @returns Promise resolving to true if user exists, false otherwise
   */
  async existsByEmail(email: string): Promise<boolean> {
    return this.users.some(user => user.email === email)
  }

  /**
   * Finds all users with a specific role.
   *
   * @param role - User role to filter by
   * @returns Promise resolving to array of users with the specified role
   */
  async findByRole(role: UserRole): Promise<User[]> {
    return this.users.filter(user => user.role === role)
  }

  /**
   * Counts the total number of users, optionally filtered.
   *
   * @param filters - Optional filtering criteria
   * @returns Promise resolving to the count of users
   */
  async count(filters?: UserFilters): Promise<number> {
    const filtered = await this.findAll(filters)
    return filtered.length
  }

  // Testing utility methods

  /**
   * Clears all users from memory and resets the ID counter.
   *
   * @description Utility method for testing to reset the repository
   * to a clean state between test cases.
   */
  clear(): void {
    this.users = []
    this.nextId = 1
  }

  /**
   * Seeds the repository with predefined user data.
   *
   * @param users - Array of users to populate the repository
   * @description Utility method for testing to set up initial data.
   * Automatically adjusts the next ID counter based on existing IDs.
   */
  seed(users: User[]): void {
    this.users = [...users]
    this.nextId = Math.max(...users.map(u => u.id), 0) + 1
  }
}

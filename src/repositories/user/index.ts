/**
 * @fileoverview User repository module exports.
 * This module provides a centralized export point for all user repository
 * related classes, interfaces, and types.
 */

// Main implementation
export { UserRepository } from './UserRepository'

// Types and interfaces
export type {
  UserRepositoryInterface,
  CreateUserRepositoryData,
  UpdateUserRepositoryData
} from './types'

// Mock for testing
export { MockUserRepository } from './mock'

/**
 * @fileoverview Prisma-based implementation of the UserRepository interface.
 * This module provides concrete data access operations for users using Prisma ORM
 * to interact with the database.
 */

import { PrismaClient } from '@prisma/client'
import { UserRepositoryInterface, CreateUserRepositoryData, UpdateUserRepositoryData } from './types'
import { User, UserFilters, UserRole } from '../../types/entities/User'

/**
 * Prisma-based implementation of user repository.
 *
 * @description Provides concrete implementation of user data access operations
 * using Prisma ORM. Handles all database interactions for user entities
 * including CRUD operations and specialized queries.
 */
export class UserRepository implements UserRepositoryInterface {
  /**
   * Creates a new UserRepository instance.
   *
   * @param prisma - Prisma client instance for database operations
   */
  constructor(private prisma: PrismaClient) {}

  /**
   * Creates a new user in the database.
   *
   * @param data - User data with hashed password ready for storage
   * @returns Promise resolving to the created user with all fields
   * @throws Error if email already exists or database constraint violation
   *
   * @example
   * ```typescript
   * const userData = {
   *   email: '<EMAIL>',
   *   hashed_password: 'hashedPassword123',
   *   role: 'ENGINEER'
   * }
   * const user = await repository.create(userData)
   * ```
   */
  async create(data: CreateUserRepositoryData): Promise<User> {
    return this.prisma.user.create({
      data: {
        email: data.email,
        hashed_password: data.hashed_password,
        role: data.role
      }
    })
  }

  /**
   * Finds a user by their unique identifier.
   *
   * @param id - User's unique identifier
   * @returns Promise resolving to user or null if not found
   *
   * @example
   * ```typescript
   * const user = await repository.findById(123)
   * if (user) {
   *   console.log(`Found user: ${user.email}`)
   * }
   * ```
   */
  async findById(id: number): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { id }
    })
  }

  /**
   * Finds a user by their email address.
   *
   * @param email - User's email address
   * @returns Promise resolving to user or null if not found
   *
   * @example
   * ```typescript
   * const user = await repository.findByEmail('<EMAIL>')
   * ```
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email }
    })
  }

  /**
   * Retrieves all users, optionally filtered by criteria.
   *
   * @param filters - Optional filtering criteria (email, role)
   * @returns Promise resolving to array of users matching the filters
   *
   * @example
   * ```typescript
   * // Get all users
   * const allUsers = await repository.findAll()
   *
   * // Get users by role
   * const engineers = await repository.findAll({ role: 'ENGINEER' })
   * ```
   */
  async findAll(filters?: UserFilters): Promise<User[]> {
    const where: Partial<Pick<User, 'email' | 'role'>> = {}

    if (filters?.email) {
      where.email = filters.email
    }

    if (filters?.role) {
      where.role = filters.role
    }

    return this.prisma.user.findMany({
      where
    })
  }

  /**
   * Updates an existing user's information.
   *
   * @param id - User's unique identifier
   * @param data - Partial user data to update
   * @returns Promise resolving to the updated user
   * @throws Error if user not found
   *
   * @example
   * ```typescript
   * const updatedUser = await repository.update(123, {
   *   email: '<EMAIL>',
   *   role: 'ADMIN'
   * })
   * ```
   */
  async update(id: number, data: UpdateUserRepositoryData): Promise<User> {
    return this.prisma.user.update({
      where: { id },
      data
    })
  }

  /**
   * Permanently deletes a user from the database.
   *
   * @param id - User's unique identifier
   * @returns Promise that resolves when deletion is complete
   * @throws Error if user not found
   *
   * @example
   * ```typescript
   * await repository.delete(123)
   * console.log('User deleted successfully')
   * ```
   */
  async delete(id: number): Promise<void> {
    await this.prisma.user.delete({
      where: { id }
    })
  }

  /**
   * Checks if a user with the given email exists.
   *
   * @param email - Email address to check
   * @returns Promise resolving to true if user exists, false otherwise
   *
   * @example
   * ```typescript
   * const exists = await repository.existsByEmail('<EMAIL>')
   * if (exists) {
   *   console.log('Email is already taken')
   * }
   * ```
   */
  async existsByEmail(email: string): Promise<boolean> {
    const user = await this.prisma.user.findUnique({
      where: { email },
      select: { id: true }
    })
    return user !== null
  }

  /**
   * Finds all users with a specific role.
   *
   * @param role - User role to filter by
   * @returns Promise resolving to array of users with the specified role
   *
   * @example
   * ```typescript
   * const admins = await repository.findByRole('ADMIN')
   * console.log(`Found ${admins.length} administrators`)
   * ```
   */
  async findByRole(role: UserRole): Promise<User[]> {
    return this.prisma.user.findMany({
      where: { role }
    })
  }

  /**
   * Counts the total number of users, optionally filtered.
   *
   * @param filters - Optional filtering criteria
   * @returns Promise resolving to the count of users
   *
   * @example
   * ```typescript
   * const totalUsers = await repository.count()
   * const engineerCount = await repository.count({ role: 'ENGINEER' })
   * ```
   */
  async count(filters?: UserFilters): Promise<number> {
    const where: Partial<Pick<User, 'email' | 'role'>> = {}

    if (filters?.email) {
      where.email = filters.email
    }

    if (filters?.role) {
      where.role = filters.role
    }

    return this.prisma.user.count({
      where
    })
  }
}

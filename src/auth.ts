import NextAuth from "next-auth"
import Credentials from "next-auth/providers/credentials"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { prisma } from "../lib/prisma"
import { validateSignInCredentials } from "./lib/validations/auth"
import { UserFactory } from "./factories/UserFactory"
import { UserRole } from "./types/entities/User"

export const { auth, handlers, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma),
  session: { strategy: "jwt" }, // Usar JWT para compatibilidad con Edge
  providers: [
    Credentials({
      // Configuración básica del proveedor de credenciales
      // Se completará en las siguientes subtareas
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      authorize: async (credentials) => {
        try {
          // Validar las credenciales usando Zod
          const validationResult = validateSignInCredentials(credentials)

          if (!validationResult.success) {
            console.error("Validation failed:", validationResult.error.errors)
            return null
          }

          const { email, password } = validationResult.data

          // Crear instancia del UserService
          const userService = UserFactory.createUserService()

          // Buscar usuario por email
          const user = await userService.getUserByEmail(email)
          if (!user) {
            console.error("User not found:", email)
            return null
          }

          // Validar contraseña usando bcryptjs
          const isPasswordValid = await userService.validatePassword(password, user)
          if (!isPasswordValid) {
            console.error("Invalid password for user:", email)
            return null
          }

          // Retornar datos del usuario para Auth.js
          console.log("Authentication successful for user:", { email, role: user.role })
          return {
            id: user.id.toString(),
            email: user.email,
            role: user.role,
          }
        } catch (error) {
          console.error("Error in authorize function:", error)
          return null
        }
      },
    }),
  ],
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  callbacks: {
    // Callback JWT: Se ejecuta cuando se crea o actualiza el JWT
    jwt: async ({ token, user }) => {
      // Durante el sign-in, el objeto 'user' contiene la información del usuario
      // que retornamos desde la función authorize
      if (user) {
        token.id = user.id as string
        token.role = user.role
      }
      return token
    },

    // Callback Session: Se ejecuta cuando se accede a la sesión
    session: async ({ session, token }) => {
      // Agregar información del token a la sesión
      if (token) {
        session.user.id = token.id as string
        session.user.role = token.role as UserRole
      }
      return session
    },
  },
})

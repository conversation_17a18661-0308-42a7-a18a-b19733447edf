/**
 * @fileoverview Factory class for creating user-related instances with proper dependency injection.
 * This module implements the Factory pattern to manage object creation and dependency injection
 * for the user domain, supporting both production and testing scenarios.
 */

import { CreateUserUseCase } from '../use-cases/CreateUserUseCase'
import { UserService } from '../services/UserService'
import { UserRepository, UserRepositoryInterface } from '../repositories/user'
import { prisma } from '../../lib/prisma'

/**
 * Factory class for creating user-related instances with dependency injection.
 *
 * @description Implements the Factory pattern to create properly configured
 * instances of user domain classes. Handles dependency injection and provides
 * both production and testing factory methods.
 *
 * @example
 * ```typescript
 * // Create a complete user controller for production
 * const controller = UserFactory.createUserController()
 *
 * // Create individual components
 * const service = UserFactory.createUserService()
 * const useCase = UserFactory.createCreateUserUseCase()
 * ```
 */
export class UserFactory {
  /**
   * Creates a UserService instance with its dependencies.
   *
   * @returns Configured UserService with UserRepository dependency
   *
   * @example
   * ```typescript
   * const userService = UserFactory.createUserService()
   * const user = await userService.createUser(userData)
   * ```
   */
  static createUserService(): UserService {
    const userRepository = new UserRepository(prisma)
    return new UserService(userRepository)
  }

  /**
   * Creates a CreateUserUseCase instance with its dependencies.
   *
   * @returns Configured CreateUserUseCase with UserService dependency
   *
   * @example
   * ```typescript
   * const useCase = UserFactory.createCreateUserUseCase()
   * const response = await useCase.execute(request)
   * ```
   */
  static createCreateUserUseCase(): CreateUserUseCase {
    const userService = this.createUserService()
    return new CreateUserUseCase(userService)
  }

  /**
   * Creates a UserRepository instance.
   *
   * @returns Configured UserRepository with Prisma client
   *
   * @example
   * ```typescript
   * const repository = UserFactory.createUserRepository()
   * const user = await repository.findById(123)
   * ```
   */
  static createUserRepository(): UserRepository {
    return new UserRepository(prisma)
  }

  /**
   * Creates a UserService for testing with injected repository.
   *
   * @param userRepository - Mock or test instance of UserRepository
   * @returns UserService configured for testing
   *
   * @description Allows injection of mock repository for unit testing.
   *
   * @example
   * ```typescript
   * const mockRepository = new MockUserRepository()
   * const service = UserFactory.createUserServiceForTesting(mockRepository)
   * ```
   */
  static createUserServiceForTesting(
    userRepository: UserRepositoryInterface
  ): UserService {
    return new UserService(userRepository)
  }
}

import { NextResponse } from "next/server"
import { auth } from "./auth"

/**
 * Middleware para proteger rutas privadas usando Auth.js v5
 *
 * Este middleware se ejecuta en todas las rutas y verifica si el usuario
 * está autenticado antes de permitir el acceso a rutas protegidas.
 */

// Rutas públicas que no requieren autenticación
const publicRoutes = [
  "/auth/signin",
  "/auth/error",
  "/api/auth",
  "/favicon.ico",
  "/_next",
  "/api/health", // Para health checks
]

// Rutas que requieren autenticación
const protectedRoutes = [
  "/",
  "/dashboard",
  "/auth/success",
  "/settings",
  "/users",
  "/actas",
  "/api/users",
  "/api/actas",
]

// Rutas específicas por rol
const roleBasedRoutes = {
  ADMIN: [
    "/admin",
    "/users",
    "/api/users",
    "/settings/system",
  ],
  ENGINEER: [
    "/actas",
    "/api/actas",
    "/reports",
  ],
  SOCIAL_ASSISTANT: [
    "/actas/view",
    "/reports/basic",
  ],
}

/**
 * Verifica si una ruta es pública
 */
function isPublicRoute(pathname: string): boolean {
  return publicRoutes.some(route => {
    if (route.endsWith("*")) {
      return pathname.startsWith(route.slice(0, -1))
    }
    return pathname.startsWith(route)
  })
}

/**
 * Verifica si una ruta requiere autenticación
 */
function isProtectedRoute(pathname: string): boolean {
  return protectedRoutes.some(route => {
    if (route === "/" && pathname === "/") return true
    if (route !== "/" && pathname.startsWith(route)) return true
    return false
  })
}

/**
 * Verifica si el usuario tiene permisos para acceder a una ruta específica
 */
function hasRolePermission(pathname: string, userRole: string): boolean {
  // Si no hay restricciones específicas de rol, permitir acceso
  const adminRoutes = roleBasedRoutes.ADMIN
  const engineerRoutes = roleBasedRoutes.ENGINEER
  const assistantRoutes = roleBasedRoutes.SOCIAL_ASSISTANT

  // Verificar si la ruta requiere permisos específicos
  const requiresAdminRole = adminRoutes.some(route => pathname.startsWith(route))
  const requiresEngineerRole = engineerRoutes.some(route => pathname.startsWith(route))
  const requiresAssistantRole = assistantRoutes.some(route => pathname.startsWith(route))

  // Si no requiere permisos específicos, permitir acceso
  if (!requiresAdminRole && !requiresEngineerRole && !requiresAssistantRole) {
    return true
  }

  // Verificar permisos según el rol
  switch (userRole) {
    case "ADMIN":
      // Los admins tienen acceso a todo
      return true
    case "ENGINEER":
      // Los ingenieros tienen acceso a rutas de ingeniero y asistente
      return !requiresAdminRole
    case "SOCIAL_ASSISTANT":
      // Los asistentes solo tienen acceso a sus rutas específicas
      return requiresAssistantRole || (!requiresAdminRole && !requiresEngineerRole)
    default:
      return false
  }
}

export default auth((req) => {
  const { pathname } = req.nextUrl
  const session = req.auth

  // Permitir acceso a rutas públicas
  if (isPublicRoute(pathname)) {
    return
  }

  // Verificar si la ruta requiere autenticación
  if (isProtectedRoute(pathname)) {
    // Si no hay sesión, redirigir al login
    if (!session) {
      const loginUrl = new URL("/auth/signin", req.url)
      loginUrl.searchParams.set("callbackUrl", pathname)
      return NextResponse.redirect(loginUrl)
    }

    // Verificar permisos de rol
    if (!hasRolePermission(pathname, session.user?.role || "")) {
      const errorUrl = new URL("/auth/error", req.url)
      errorUrl.searchParams.set("error", "AccessDenied")
      return NextResponse.redirect(errorUrl)
    }
  }
})



// Configuración del matcher para especificar en qué rutas se ejecuta el middleware
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
}

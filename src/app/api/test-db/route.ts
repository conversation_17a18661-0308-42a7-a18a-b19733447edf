import { NextResponse } from 'next/server'
import { prisma } from '../../../../lib/prisma'

export async function GET() {
  try {
    // Verificar conexión a la base de datos
    await prisma.$connect()

    // Hacer una consulta de prueba
    const result = await prisma.$queryRaw`SELECT 1 as test, datetime('now') as timestamp`

    // Convertir BigInt a string para serialización JSON
    const serializedResult = JSON.parse(JSON.stringify(result, (key, value) =>
      typeof value === 'bigint' ? value.toString() : value
    ))

    return NextResponse.json({
      success: true,
      message: 'Conexión a la base de datos exitosa',
      data: serializedResult,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error de conexión a la base de datos:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Error de conexión a la base de datos',
        error: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}

"use client"

import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { CheckCircle, User, Mail, Shield, LogOut, Home, Settings, Clock } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function AuthSuccessPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [currentTime, setCurrentTime] = useState<string>("")

  useEffect(() => {
    // Actualizar la hora cada segundo
    const updateTime = () => {
      setCurrentTime(new Date().toLocaleString('es-ES', {
        timeZone: 'America/Mexico_City',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }))
    }

    updateTime()
    const interval = setInterval(updateTime, 1000)

    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    // Redirigir al login si no hay sesión
    if (status === "unauthenticated") {
      router.push("/auth/signin")
    }
  }, [status, router])

  const handleSignOut = async () => {
    await signOut({ 
      callbackUrl: "/auth/signin",
      redirect: true 
    })
  }

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "Administrador"
      case "ENGINEER":
        return "Ingeniero"
      case "SOCIAL_ASSISTANT":
        return "Asistente Social"
      default:
        return role
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "text-red-600 bg-red-50"
      case "ENGINEER":
        return "text-blue-600 bg-blue-50"
      case "SOCIAL_ASSISTANT":
        return "text-green-600 bg-green-50"
      default:
        return "text-gray-600 bg-gray-50"
    }
  }

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Verificando sesión...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null // El useEffect se encargará de la redirección
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto space-y-8">
        {/* Header de bienvenida */}
        <div className="text-center">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100 mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">
            ¡Autenticación Exitosa!
          </h1>
          <p className="mt-2 text-gray-600">
            Bienvenido al Sistema de Actas de Vecindad
          </p>
        </div>

        {/* Información del usuario */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Información del Usuario
            </CardTitle>
            <CardDescription>
              Detalles de tu cuenta y sesión actual
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <Mail className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Email</p>
                  <p className="text-sm text-gray-600">{session.user?.email}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <Shield className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Rol</p>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(session.user?.role || '')}`}>
                    {getRoleDisplayName(session.user?.role || '')}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Clock className="h-5 w-5 text-gray-500" />
              <div>
                <p className="text-sm font-medium text-gray-900">Hora actual</p>
                <p className="text-sm text-gray-600">{currentTime}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Información de la sesión (para debugging) */}
        <Card>
          <CardHeader>
            <CardTitle>Información de Sesión</CardTitle>
            <CardDescription>
              Datos técnicos de la sesión (útil para desarrollo)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertDescription>
                <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                  {JSON.stringify(session, null, 2)}
                </pre>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Acciones */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Button 
            onClick={() => router.push("/")}
            className="flex-1"
          >
            <Home className="mr-2 h-4 w-4" />
            Ir al Dashboard
          </Button>
          
          <Button 
            variant="outline"
            onClick={() => router.push("/settings")}
            className="flex-1"
          >
            <Settings className="mr-2 h-4 w-4" />
            Configuración
          </Button>
          
          <Button 
            variant="destructive"
            onClick={handleSignOut}
            className="flex-1"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Cerrar Sesión
          </Button>
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>Sistema de Actas de Vecindad v1.0</p>
          <p>Desarrollado con Next.js 15 + Auth.js v5</p>
        </div>
      </div>
    </div>
  )
}

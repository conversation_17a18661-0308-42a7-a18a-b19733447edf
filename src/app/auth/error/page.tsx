"use client"

import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON><PERSON><PERSON>gle, ArrowLeft, RefreshCw } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const error = searchParams.get("error")

  const getErrorInfo = (errorCode: string | null) => {
    switch (errorCode) {
      case "Configuration":
        return {
          title: "Error de Configuración",
          description: "Hay un problema con la configuración del servidor de autenticación.",
          suggestion: "Por favor, contacta al administrador del sistema.",
        }
      case "AccessDenied":
        return {
          title: "Acceso Denegado",
          description: "No tienes permisos para acceder a este recurso.",
          suggestion: "Verifica que tengas los permisos necesarios o contacta al administrador.",
        }
      case "Verification":
        return {
          title: "Error de Verificación",
          description: "No se pudo verificar tu identidad.",
          suggestion: "Por favor, intenta iniciar sesión nuevamente.",
        }
      case "Default":
      case "CredentialsSignin":
        return {
          title: "Error de Autenticación",
          description: "Las credenciales proporcionadas son incorrectas.",
          suggestion: "Verifica tu email y contraseña, luego intenta de nuevo.",
        }
      case "EmailSignin":
        return {
          title: "Error de Email",
          description: "No se pudo enviar el email de verificación.",
          suggestion: "Verifica que tu dirección de email sea correcta.",
        }
      case "OAuthSignin":
      case "OAuthCallback":
      case "OAuthCreateAccount":
      case "OAuthAccountNotLinked":
        return {
          title: "Error de OAuth",
          description: "Hubo un problema con el proveedor de autenticación externo.",
          suggestion: "Por favor, intenta con un método de autenticación diferente.",
        }
      case "SessionRequired":
        return {
          title: "Sesión Requerida",
          description: "Necesitas iniciar sesión para acceder a esta página.",
          suggestion: "Por favor, inicia sesión para continuar.",
        }
      default:
        return {
          title: "Error de Autenticación",
          description: "Ocurrió un error inesperado durante el proceso de autenticación.",
          suggestion: "Por favor, intenta de nuevo. Si el problema persiste, contacta al soporte técnico.",
        }
    }
  }

  const errorInfo = getErrorInfo(error)

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <CardHeader className="space-y-1 text-center">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              {errorInfo.title}
            </CardTitle>
            <CardDescription className="text-gray-600">
              {errorInfo.description}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {errorInfo.suggestion}
              </AlertDescription>
            </Alert>

            {error && (
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Código de error:</span> {error}
                </p>
              </div>
            )}

            <div className="flex flex-col space-y-3">
              <Button asChild className="w-full">
                <Link href="/auth/signin">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Volver al Login
                </Link>
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => window.location.reload()}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Intentar de Nuevo
              </Button>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-500">
                ¿Necesitas ayuda?{" "}
                <a 
                  href="mailto:<EMAIL>" 
                  className="font-medium text-blue-600 hover:text-blue-500"
                >
                  Contacta al soporte técnico
                </a>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

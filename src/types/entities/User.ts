/**
 * @fileoverview User domain types and interfaces for the Actas de Vecindad application.
 * This module contains all type definitions related to user entities, including
 * data transfer objects (DTOs) for API communication and repository operations.
 */

/**
 * Represents the possible roles a user can have in the system.
 *
 * @description Defines the authorization levels available:
 * - ADMIN: Full system access and user management
 * - ENGINEER: Technical operations and data management
 * - SOCIAL_ASSISTANT: Community interaction and support functions
 */
export type UserRole = 'ADMIN' | 'ENGINEER' | 'SOCIAL_ASSISTANT'

/**
 * Complete user entity as stored in the database.
 *
 * @description Represents a user with all database fields including
 * sensitive information like hashed passwords. This interface should
 * only be used internally and never exposed to API responses.
 */
export interface User {
  /** Unique identifier for the user */
  id: number
  /** User's email address (unique) */
  email: string
  /** Bcrypt hashed password */
  hashed_password: string
  /** User's role determining system permissions */
  role: UserRole
  /** Timestamp when the user was created */
  created_at: Date
  /** Timestamp when the user was last updated */
  updated_at: Date
}

/**
 * Data required to create a new user through the service layer.
 *
 * @description Contains the plain text password which will be hashed
 * before being stored in the repository.
 */
export interface CreateUserData {
  /** User's email address */
  email: string
  /** Plain text password (will be hashed) */
  password: string
  /** User's role in the system */
  role: UserRole
}

/**
 * Data structure for creating a user at the repository level.
 *
 * @description Contains the hashed password ready for database storage.
 * This interface is used between the service and repository layers.
 */
export interface CreateUserRepositoryData {
  /** User's email address */
  email: string
  /** Pre-hashed password ready for storage */
  hashed_password: string
  /** User's role in the system */
  role: UserRole
}

/**
 * Data structure for updating user information.
 *
 * @description All fields are optional to support partial updates.
 * The hashed_password field should contain a pre-hashed value.
 */
export interface UpdateUserData {
  /** Updated email address */
  email?: string
  /** Updated hashed password */
  hashed_password?: string
  /** Updated user role */
  role?: UserRole
}

/**
 * Filter criteria for querying users.
 *
 * @description Used in repository methods to filter user queries.
 * All fields are optional to support flexible filtering.
 */
export interface UserFilters {
  /** Filter by email address */
  email?: string
  /** Filter by user role */
  role?: UserRole
}

// DTOs para API

/**
 * Request payload for creating a new user via API.
 *
 * @description Contains the data sent from the client to create a user.
 * The password is in plain text and will be hashed by the service layer.
 */
export interface CreateUserRequest {
  /** User's email address */
  email: string
  /** Plain text password */
  password: string
  /** User's role in the system */
  role: UserRole
}

/**
 * Response payload for successful user creation.
 *
 * @description Returns the created user without sensitive information
 * like the hashed password.
 */
export interface CreateUserResponse {
  /** Created user data without sensitive fields */
  user: Omit<User, 'hashed_password'>
}

/**
 * User data safe for API responses.
 *
 * @description Represents user information without sensitive data
 * like passwords. Used in API responses and client-side operations.
 */
export interface UserResponse {
  /** Unique identifier for the user */
  id: number
  /** User's email address */
  email: string
  /** User's role in the system */
  role: UserRole
  /** Timestamp when the user was created */
  created_at: Date
  /** Timestamp when the user was last updated */
  updated_at: Date
}
